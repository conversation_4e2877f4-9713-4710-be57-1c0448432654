body {
    font-family: 'Poppins', sans-serif;
    margin: 0;
    overflow-x: hidden;
}

#hero-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #000;
}

.hero-content {
    position: relative;
    z-index: 1;
}

/* Animation keyframes */
@keyframes float {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-20px); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Service card hover effects */
.service-card {
    position: relative;
    background: linear-gradient(to bottom right, #ffffff, #f8fafc);
    border: 1px solid rgba(229, 231, 235, 0.5);
    border-radius: 1.5rem;
    overflow: hidden;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    isolation: isolate;
    opacity: 0;
    transform: translateY(20px);
}

.service-card.animate-on-scroll {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.service-card.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* Service cards animation delay */
.service-card:nth-child(1) { transition-delay: 0s; }
.service-card:nth-child(2) { transition-delay: 0.1s; }
.service-card:nth-child(3) { transition-delay: 0.2s; }
.service-card:nth-child(4) { transition-delay: 0.3s; }

.service-card::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(
        45deg,
        transparent,
        rgba(239, 68, 68, 0.03) 40%,
        rgba(239, 68, 68, 0.05) 60%,
        transparent
    );
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.service-card::after {
    content: '';
    position: absolute;
    inset: 0;
    background: radial-gradient(
        circle at top right,
        rgba(239, 68, 68, 0.05),
        transparent 60%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.service-card:hover {
    transform: translateY(-8px);
    border-color: rgba(239, 68, 68, 0.2);
    box-shadow: 
        0 20px 40px -15px rgba(0, 0, 0, 0.1),
        0 0 20px rgba(239, 68, 68, 0.1);
}

.service-card:hover::before,
.service-card:hover::after {
    opacity: 1;
}

.service-card .card-content {
    position: relative;
    z-index: 2;
    padding: 2rem;
}

.service-card .icon-wrapper {
    position: relative;
    width: 3.5rem;
    height: 3.5rem;
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.05));
    border-radius: 1rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.service-card:hover .icon-wrapper {
    transform: scale(1.1) rotate(5deg);
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(239, 68, 68, 0.1));
}

.service-card .icon-wrapper i {
    font-size: 1.5rem;
    color: rgb(239, 68, 68);
    transition: all 0.3s ease;
}

.service-card:hover .icon-wrapper i {
    transform: scale(1.1);
    color: rgb(220, 38, 38);
}

.service-card h3 {
    color: #1a1a1a;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    transition: all 0.3s ease;
}

.service-card:hover h3 {
    color: rgb(239, 68, 68);
}

.service-card p {
    color: #4b5563;
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.service-card .learn-more {
    display: flex;
    align-items: center;
    color: rgb(239, 68, 68);
    font-size: 0.875rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.service-card .learn-more i {
    margin-left: 0.5rem;
    transition: transform 0.3s ease;
}

.service-card:hover .learn-more {
    color: rgb(220, 38, 38);
}

.service-card:hover .learn-more i {
    transform: translateX(4px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .service-card .card-content {
        padding: 1.5rem;
    }

    .service-card .icon-wrapper {
        width: 3rem;
        height: 3rem;
    }

    .service-card .icon-wrapper i {
        font-size: 1.25rem;
    }

    .service-card h3 {
        font-size: 1.125rem;
    }

    .service-card p {
        font-size: 0.875rem;
    }
}

/* Gradient animations */
.gradient-animate {
    background-size: 200% 200%;
    animation: gradientShift 5s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Button hover effects */
.hover-effect {
    transition: all 0.3s ease;
}

.hover-effect:hover {
    transform: translateY(-2px);
}

.glow-effect:hover {
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
}

/* Timeline animations */
.timeline-dot {
    transition: all 0.3s ease;
}

.timeline-item:hover .timeline-dot {
    transform: scale(1.2);
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.4);
}

/* Additional styles */
.fade-in-section {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.fade-in-section.is-visible {
    opacity: 1;
    transform: translateY(0);
}

.about-card {
    transition: all 0.4s ease;
}

.about-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(239, 68, 68, 0.1);
}

.about-icon {
    transition: all 0.4s ease;
}

.about-card:hover .about-icon {
    transform: scale(1.1) rotate(12deg);
}

.stat-card {
    transition: all 0.4s ease;
    cursor: pointer;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(239, 68, 68, 0.1);
}

.service-card {
    cursor: pointer;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(239, 68, 68, 0.1);
}

.animate-fade-up {
    animation: fadeUp 0.6s ease-out forwards;
}

@keyframes fadeUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Add these styles to your existing styles section */
.nav-link {
    position: relative;
    color: white;
    text-decoration: none;
    padding: 0.5rem 0;
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: #ef4444;
    transition: width 0.3s ease-in-out;
}

.nav-link:hover::after {
    width: 100%;
}

.nav-link.active::after {
    width: 100%;
}

@media (max-width: 768px) {
    #mobile-menu {
        transform-origin: top;
        transition: all 0.3s ease-in-out;
    }

    #mobile-menu.hidden {
        display: none;
        transform: scaleY(0);
    }

    #mobile-menu:not(.hidden) {
        display: block;
        transform: scaleY(1);
    }
}

/* Add scroll effect for navigation */
nav {
    transition: all 0.3s ease-in-out;
}

nav.scrolled {
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Smooth scroll behavior for the entire page */
html {
    scroll-behavior: smooth;
}

/* Animation classes */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* Stats animation delay */
.stat-card:nth-child(1) { transition-delay: 0s; }
.stat-card:nth-child(2) { transition-delay: 0.1s; }
.stat-card:nth-child(3) { transition-delay: 0.2s; }
.stat-card:nth-child(4) { transition-delay: 0.3s; }

/* Modal animations */
.modal {
    transition: opacity 0.3s ease-out, visibility 0.3s ease-out;
    opacity: 0;
    visibility: hidden;
}

.modal.flex {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    transform: scale(0.95);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    max-height: 90vh;
}

/* Modal header enhancements */
.modal-header {
    background: #ffffff;
    border-bottom: 1px solid rgba(229, 231, 235, 0.5);
    padding: 1.25rem;
    position: sticky;
    top: 0;
    z-index: 10;
    flex-shrink: 0;
}

.modal-header h3 {
    color: #111827;
    font-size: 1.5rem;
    line-height: 1.2;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.modal-header h3 i {
    color: #ef4444;
    font-size: 1.25rem;
}

/* Updated close button style */
.modal-header .modal-close {
    background: transparent;
    padding: 0.5rem;
    transition: all 0.3s ease;
    color: #6b7280;
}

.modal-header .modal-close:hover {
    color: #ef4444;
    transform: rotate(90deg);
    background: transparent;
}

/* Update modal content spacing */
.modal-content {
    margin: 1.5rem;
    max-width: 42rem;
    width: 100%;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.modal-body {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem;
    background: #ffffff;
    border-radius: 0 0 0.75rem 0.75rem;
    scrollbar-width: thin;
    scrollbar-color: rgba(220, 38, 38, 0.5) rgba(229, 231, 235, 0.5);
}

/* Customize scrollbar for webkit browsers */
.modal-body::-webkit-scrollbar {
    width: 8px;
}

.modal-body::-webkit-scrollbar-track {
    background: rgba(229, 231, 235, 0.5);
    border-radius: 0 0 0.75rem 0;
}

.modal-body::-webkit-scrollbar-thumb {
    background-color: rgba(220, 38, 38, 0.5);
    border-radius: 20px;
    border: 2px solid rgba(229, 231, 235, 0.5);
}

/* Update service item styles */
.service-item {
    background: #f9fafb;
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    border: 1px solid transparent;
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    cursor: pointer;
    -webkit-tap-highlight-color: transparent;
}

.service-item:last-child {
    margin-bottom: 0;
}

/* Desktop hover effects */
@media (min-width: 769px) {
    .service-item:hover {
        background: #ffffff;
        border-color: #fee2e2;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }

    .service-item:hover i {
        transform: scale(1.1);
        background: #fecaca;
    }

    .service-item:hover h4 {
        color: #dc2626;
    }
}

/* Mobile touch states */
@media (max-width: 768px) {
    .service-item {
        padding: 1.25rem;
        margin-bottom: 0.75rem;
    }

    .service-item:active {
        background: #ffffff;
        border-color: #fee2e2;
        transform: scale(0.98);
        transition: all 0.1s ease;
    }

    .service-item i {
        width: 3rem;
        height: 3rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #fee2e2;
        border-radius: 0.75rem;
        font-size: 1.25rem;
        color: #dc2626;
        transition: all 0.2s ease;
    }

    .service-item:active i {
        transform: scale(0.95);
        background: #fecaca;
    }

    .service-item h4 {
        font-size: 1.125rem;
        margin-bottom: 0.5rem;
        color: #1f2937;
        font-weight: 600;
        transition: color 0.2s ease;
    }

    .service-item:active h4 {
        color: #dc2626;
    }

    .service-item p {
        font-size: 0.875rem;
        color: #6b7280;
        line-height: 1.5;
    }

    /* Add ripple effect for touch feedback */
    .service-item {
        position: relative;
        overflow: hidden;
    }

    .service-item::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 5px;
        height: 5px;
        background: rgba(239, 68, 68, 0.3);
        opacity: 0;
        border-radius: 100%;
        transform: scale(1, 1) translate(-50%);
        transform-origin: 50% 50%;
    }

    .service-item:active::after {
        animation: ripple 0.4s ease-out;
    }

    @keyframes ripple {
        0% {
            transform: scale(0, 0);
            opacity: 0.5;
        }
        100% {
            transform: scale(40, 40);
            opacity: 0;
        }
    }

    /* Improve scroll behavior */
    .modal-body {
        -webkit-overflow-scrolling: touch;
        scroll-behavior: smooth;
        padding: 1rem;
        overscroll-behavior: contain;
    }
}

.service-item i {
    background: #fee2e2;
    padding: 1rem;
    border-radius: 0.75rem;
    font-size: 1.5rem;
    color: #dc2626;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.service-item:hover i {
    transform: scale(1.1);
    background: #fecaca;
}

.service-item .content {
    flex: 1;
}

.service-item h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1f2937;
    transition: color 0.3s ease;
}

.service-item:hover h4 {
    color: #dc2626;
}

.service-item p {
    color: #6b7280;
    line-height: 1.5;
}

.modal-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 1.5rem;
    color: #6b7280;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.modal-close:hover {
    transform: rotate(90deg);
    color: #ef4444;
}

@media (min-width: 768px) {
    .modal-content {
        max-width: 500px;
        padding: 1.5rem;
    }

    .modal-header h3 {
        font-size: 2rem;
    }

    .service-item h4 {
        font-size: 1.5rem;
    }

    .service-item p {
        font-size: 1.125rem;
    }
}

/* Update modal styles for better responsiveness */
.modal-content {
    max-width: 95%;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    margin: 1rem;
    border-radius: 1rem;
}

/* Leadership Modal Specific Styles */
.leadership-modal {
    max-width: 95%;
    width: 100%;
    max-height: 90vh;
    overflow: hidden;
    background: #ffffff;
    border-radius: 1.5rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    display: flex;
    flex-direction: column;
}

/* Responsive width adjustments */
@media (min-width: 1024px) {
    .leadership-modal {
        max-width: 75%;
    }
}

@media (min-width: 1280px) {
    .leadership-modal {
        max-width: 1100px; /* Fixed maximum width */
    }
}

/* Update image height for better proportions */
.leadership-modal .lg\:col-span-5 {
    position: relative;
}

.leadership-modal .lg\:col-span-5 img {
    height: 100%;
    object-fit: cover;
    max-height: 700px; /* Maximum height for the image */
}

/* Adjust content padding for better spacing */
.leadership-modal .lg\:col-span-7 {
    padding: 2.5rem;
    max-height: 700px; /* Match image height */
    overflow-y: auto;
}

/* Ensure modal stays centered */
.modal.flex {
    padding: 2rem;
}

/* Leadership Modal Header Adjustments */
.leadership-modal .modal-header {
    background: #ffffff;
    border-bottom: 1px solid rgba(229, 231, 235, 0.5);
    padding: 1.25rem; /* Reduced padding */
    position: sticky;
    top: 0;
    z-index: 10;
    flex-shrink: 0;
}

.leadership-modal .modal-header h3 {
    color: #111827;
    font-size: 1.5rem; /* Reduced font size */
    line-height: 1.2;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.leadership-modal .modal-header p {
    color: #ef4444;
    font-size: 1rem; /* Reduced font size */
    font-weight: 500;
}

/* Updated close button style */
.leadership-modal .modal-close {
    background: transparent;
    padding: 0.5rem;
    transition: all 0.3s ease;
    color: #6b7280;
}

.leadership-modal .modal-close:hover {
    color: #ef4444;
    transform: rotate(90deg);
    background: transparent;
}

.leadership-modal .modal-body {
    padding: 0;
    overflow-y: auto; /* Enable vertical scrolling */
    flex: 1; /* Take remaining space */
}

/* Update grid container to maintain height */
.leadership-modal .modal-body .grid {
    min-height: min-content;
    height: 100%;
}

/* Ensure content column is scrollable if needed */
.leadership-modal .lg\:col-span-7 {
    overflow-y: auto;
    max-height: calc(90vh - 5rem); /* Account for header height */
}

/* Keep image column fixed */
.leadership-modal .lg\:col-span-5 {
    position: sticky;
    top: 0;
}

/* Responsive adjustments */
@media (max-width: 1023px) {
    .leadership-modal .lg\:col-span-7 {
        max-height: none; /* Remove max-height on mobile */
        overflow-y: visible;
    }
    
    .leadership-modal .lg\:col-span-5 {
        position: relative;
    }
}

.leadership-modal .content-section {
    padding: 2rem;
}

.leadership-modal .section-title {
    color: #111827;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.leadership-modal .section-title i {
    color: #ef4444;
    font-size: 1.25rem;
}

.leadership-modal .expertise-grid {
    display: grid;
    gap: 1rem;
    margin: 1.5rem 0;
}

.leadership-modal .expertise-item {
    background: rgba(249, 250, 251, 0.8);
    border: 1px solid rgba(229, 231, 235, 0.5);
    border-radius: 0.75rem;
    padding: 1rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.leadership-modal .expertise-item:hover {
    background: #ffffff;
    border-color: #ef4444;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.leadership-modal .social-links {
    display: flex;
    gap: 1.5rem;
    margin-top: 2rem;
}

.leadership-modal .social-links a {
    color: #6b7280;
    font-size: 1.5rem;
    transition: all 0.3s ease;
}

.leadership-modal .social-links a:hover {
    color: #ef4444;
    transform: translateY(-3px);
}

.leadership-modal .profile-image {
    position: relative;
    height: 100%;
    min-height: 400px;
}

.leadership-modal .profile-image img {
    object-fit: cover;
    width: 100%;
    height: 100%;
}

.leadership-modal .profile-image::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(to top, 
        rgba(0, 0, 0, 0.7) 0%, 
        rgba(0, 0, 0, 0.3) 50%, 
        rgba(0, 0, 0, 0) 100%);
}

/* Add/update these styles in the <style> section */
.top-bar {
    background: rgba(0, 0, 0, 0.95);
    padding: 0.5rem 0;
    color: #fff;
    font-size: 0.875rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: none; /* Hidden by default on mobile */
}

.top-bar .contact-info,
.top-bar .phone-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.top-bar i {
    font-size: 1rem;
    width: 1.5rem;
    height: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(239, 68, 68, 0.1);
    border-radius: 50%;
    transition: all 0.3s ease;
}

.top-bar a {
    color: #fff;
    text-decoration: none;
    transition: color 0.3s ease;
}

.top-bar a:hover {
    color: #ef4444;
}

.top-bar a:hover i {
    background: rgba(239, 68, 68, 0.2);
    transform: scale(1.1);
}

/* Responsive styles */
@media (min-width: 768px) {
    .top-bar {
        display: block; /* Show on desktop */
    }
}

/* Add these new styles for floating icons */
.floating-icons-container {
    position: relative;
    width: 100%;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.floating-icons-wrapper {
    position: relative;
    width: 100%;
    height: 400px;
    margin: 0 auto;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    background: rgba(15, 23, 42, 0.3);
    backdrop-filter: blur(8px);
    box-shadow: 
        0 0 40px rgba(0, 0, 0, 0.2),
        inset 0 0 20px rgba(255, 255, 255, 0.05);
    overflow: hidden;
}

.floating-icons-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
}

.floating-icons-wrapper::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
}

.floating-icon {
    position: absolute;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(15, 23, 42, 0.75);
    backdrop-filter: blur(8px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 
        0 8px 32px -8px rgba(0, 0, 0, 0.3),
        inset 0 0 0 1px rgba(255, 255, 255, 0.1);
    cursor: pointer;
    will-change: transform;
}

.floating-icon i {
    font-size: 1.5rem;
    color: var(--icon-color);
    filter: drop-shadow(0 0 8px var(--glow-color));
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.floating-icon:hover {
    transform: scale(1.15);
    background: rgba(15, 23, 42, 0.85);
    box-shadow: 
        0 12px 32px -8px rgba(0, 0, 0, 0.4),
        0 0 16px var(--glow-color),
        inset 0 0 0 1px rgba(255, 255, 255, 0.2);
    z-index: 10;
}

.floating-icon:hover i {
    transform: scale(1.1);
    filter: drop-shadow(0 0 12px var(--glow-color));
}

/* Adjust responsive sizing */
@media (max-width: 1280px) {
    .floating-icons-wrapper {
        height: 350px;
    }

    .floating-icon {
        width: 50px;
        height: 50px;
    }

    .floating-icon i {
        font-size: 1.25rem;
    }
}

@media (max-width: 768px) {
    .floating-icons-wrapper {
        height: 300px;
    }

    .floating-icon {
        width: 45px;
        height: 45px;
    }

    .floating-icon i {
        font-size: 1.125rem;
    }
}

/* Keep the spin animation only for the settings icon */
.spin-slow {
    animation: spin 12s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Add these styles to your existing styles section */
#mobile-menu {
    transition: all 0.3s ease-in-out;
}

#mobile-menu-button {
    transition: all 0.3s ease-in-out;
}

.mobile-menu-item {
    position: relative;
    overflow: hidden;
}

.mobile-menu-item::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 0;
    background: rgba(239, 68, 68, 0.1);
    transition: height 0.3s ease;
    z-index: -1;
    border-radius: inherit;
}

.mobile-menu-item:hover::after {
    height: 100%;
}

.mobile-menu-item i {
    transition: transform 0.3s ease;
}

.mobile-menu-item:hover i {
    transform: scale(1.2) rotate(10deg);
}

#mobile-menu .social-links a {
    position: relative;
    overflow: hidden;
}

#mobile-menu .social-links a::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(239, 68, 68, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

#mobile-menu .social-links a:hover::before {
    width: 200%;
    height: 200%;
}

/* Add these styles to your existing styles section */
.mobile-menu-button {
    position: relative;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: transparent;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 50;
}

.mobile-menu-button:hover {
    background: rgba(239, 68, 68, 0.1);
}

.mobile-menu-button .hamburger {
    width: 24px;
    height: 20px;
    position: relative;
    transform: rotate(0deg);
    transition: 0.3s ease-in-out;
    cursor: pointer;
}

.mobile-menu-button .hamburger span {
    display: block;
    position: absolute;
    height: 2px;
    width: 100%;
    background: #ffffff;
    border-radius: 2px;
    opacity: 1;
    left: 0;
    transform: rotate(0deg);
    transition: .25s ease-in-out;
}

.mobile-menu-button .hamburger span:nth-child(1) {
    top: 0px;
    transform-origin: left center;
}

.mobile-menu-button .hamburger span:nth-child(2) {
    top: 9px;
    transform-origin: left center;
}

.mobile-menu-button .hamburger span:nth-child(3) {
    top: 18px;
    transform-origin: left center;
}

.mobile-menu-button.open .hamburger span:nth-child(1) {
    transform: rotate(45deg);
    top: -1px;
    left: 4px;
}

.mobile-menu-button.open .hamburger span:nth-child(2) {
    width: 0%;
    opacity: 0;
}

.mobile-menu-button.open .hamburger span:nth-child(3) {
    transform: rotate(-45deg);
    top: 19px;
    left: 4px;
}

/* Update mobile menu styles */
@media (min-width: 768px) {
    #mobile-menu,
    #mobile-menu-button {
        display: none !important;
    }
}

#mobile-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    padding: 0.75rem;
    transform-origin: top;
    transform: scaleY(0);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 40;
}

#mobile-menu.show {
    transform: scaleY(1);
    opacity: 1;
}

.mobile-menu-item {
    position: relative;
    overflow: hidden;
    background: transparent;
    transition: all 0.3s ease;
}

.mobile-menu-item:hover {
    background: rgba(239, 68, 68, 0.1);
}

.mobile-menu-item:hover .flex-shrink-0 {
    transform: scale(1.1);
    border-color: rgba(239, 68, 68, 0.4);
    background: linear-gradient(to bottom right, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.1));
}

.mobile-menu-item:hover i {
    transform: scale(1.1);
    color: #ef4444;
}

.mobile-menu-item .flex-shrink-0 {
    transition: all 0.3s ease;
}

.mobile-menu-item i {
    transition: all 0.3s ease;
    font-size: 0.875rem;
}

/* Add these styles to your existing styles section */
.timeline-container {
    position: relative;
    padding: 2rem 0;
}

.timeline-line {
    overflow: hidden;
    opacity: 0.3;
}

.timeline-line-inner {
    height: 0;
    animation: line-grow 2s ease forwards;
}

.timeline-dot-container {
    z-index: 10;
}

.timeline-dot {
    width: 24px;
    height: 24px;
    background: #ef4444;
    border-radius: 50%;
    border: 4px solid white;
    box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.2);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.timeline-item:hover .timeline-dot {
    transform: scale(1.3);
    box-shadow: 0 0 0 8px rgba(239, 68, 68, 0.2);
    background: #dc2626;
}

.timeline-content {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.timeline-item.aos-animate .timeline-content {
    opacity: 1;
    transform: translateY(0);
}

.timeline-card {
    position: relative;
    overflow: hidden;
    transform-origin: center;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.timeline-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(239, 68, 68, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.5s ease;
}

.timeline-card:hover {
    transform: translateY(-5px) scale(1.02);
}

.timeline-card:hover::before {
    opacity: 1;
}

.timeline-icon {
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.timeline-card:hover .timeline-icon {
    transform: scale(1.2) rotate(15deg);
    color: #dc2626;
}

.timeline-date {
    position: relative;
    display: inline-block;
    padding: 4px 12px;
    background: rgba(239, 68, 68, 0.1);
    border-radius: 20px;
    transition: all 0.3s ease;
}

.timeline-item:hover .timeline-date {
    background: rgba(239, 68, 68, 0.2);
    transform: translateY(-2px);
}

.timeline-title {
    position: relative;
    display: inline-block;
}

.timeline-title::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -4px;
    width: 0;
    height: 2px;
    background: #ef4444;
    transition: width 0.3s ease;
}

.timeline-item:hover .timeline-title::after {
    width: 100%;
}

@keyframes line-grow {
    from {
        height: 0;
    }
    to {
        height: 100%;
    }
}

.animate-fade-up {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeUp 0.6s ease-out forwards;
}

@keyframes fadeUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Add AOS (Animate On Scroll) styles */
[data-aos] {
    opacity: 0;
    transition-property: transform, opacity;
}

[data-aos].aos-animate {
    opacity: 1;
}

[data-aos="fade-up"] {
    transform: translateY(20px);
}

[data-aos="fade-up"].aos-animate {
    transform: translateY(0);
}

/* Add these responsive timeline styles */
@media (max-width: 768px) {
    .timeline-container {
        padding: 1rem 0;
    }

    .timeline-line {
        left: 2rem !important;
        transform: none !important;
    }

    .timeline-item {
        margin-bottom: 3rem;
    }

    .timeline-item .flex {
        flex-direction: column;
        align-items: flex-start;
    }

    .timeline-dot-container {
        left: 2rem !important;
        transform: none !important;
        top: 1.5rem;
    }

    .timeline-content {
        width: 100% !important;
        padding-left: 4rem !important;
        padding-right: 0 !important;
        text-align: left !important;
    }

    .timeline-card {
        width: 100%;
        margin-top: 0.5rem;
    }

    .timeline-date {
        margin-left: 0;
    }

    .space-y-24 {
        margin-top: 2rem;
    }

    /* Adjust spacing for mobile */
    .timeline-item {
        padding-bottom: 2rem;
    }

    .timeline-item:last-child {
        padding-bottom: 0;
    }

    /* Make cards consistent on mobile */
    .timeline-card {
        border-left: 4px solid #ef4444 !important;
        border-right: none !important;
    }

    /* Adjust text alignment */
    .timeline-content .flex {
        justify-content: flex-start !important;
    }

    .timeline-content .justify-end {
        justify-content: flex-start !important;
    }

    /* Improve spacing between items */
    .space-y-24 > :not([hidden]) ~ :not([hidden]) {
        --tw-space-y-reverse: 0;
        margin-top: calc(3rem * calc(1 - var(--tw-space-y-reverse)));
        margin-bottom: calc(3rem * var(--tw-space-y-reverse));
    }
}

/* Add smooth reveal animation for mobile */
@media (max-width: 768px) {
    .timeline-item {
        opacity: 0;
        transform: translateX(-20px);
        transition: all 0.6s ease;
    }

    .timeline-item.aos-animate {
        opacity: 1;
        transform: translateX(0);
    }

    .timeline-line-inner {
        animation: line-grow-mobile 2s ease forwards;
    }

    @keyframes line-grow-mobile {
        from {
            height: 0;
        }
        to {
            height: calc(100% - 2rem);
        }
    }
}

/* Add these mobile-friendly modal styles */
@media (max-width: 768px) {
    .modal-content {
        margin: 0.75rem;
        max-height: 90vh;
        width: calc(100% - 1.5rem);
        border-radius: 1rem;
    }

    .modal-header {
        padding: 1rem;
        border-bottom: 1px solid rgba(229, 231, 235, 0.5);
    }

    .modal-header h3 {
        font-size: 1.25rem;
        line-height: 1.4;
    }

    .modal-body {
        padding: 1rem;
        max-height: calc(90vh - 4rem);
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }

    .service-item {
        padding: 1rem;
        margin-bottom: 0.75rem;
        gap: 0.75rem;
    }

    .service-item .icon-container {
        flex-shrink: 0;
        width: 2.5rem;
        height: 2.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(239, 68, 68, 0.1);
        border-radius: 0.75rem;
        margin-right: 1rem;
        color: #ef4444;
    }

    .service-item h4 {
        font-size: 1.125rem;
        margin-bottom: 0.5rem;
        color: #1f2937;
        font-weight: 600;
    }

    .service-item p {
        font-size: 0.875rem;
        color: #6b7280;
        line-height: 1.5;
    }

    .service-item .content {
        flex: 1;
    }

    /* Improve modal animations for mobile */
    .modal {
        background-color: rgba(0, 0, 0, 0.75);
        backdrop-filter: blur(4px);
    }

    .modal-content {
        transform: translateY(20px);
        opacity: 0;
        transition: all 0.3s ease-out;
    }

    .modal.flex .modal-content {
        transform: translateY(0);
        opacity: 1;
    }

    /* Add touch-friendly close button */
    .modal-close {
        position: absolute;
        top: 0.75rem;
        right: 0.75rem;
        width: 2.5rem;
        height: 2.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(239, 68, 68, 0.1);
        border-radius: 50%;
        color: #ef4444;
        transition: all 0.3s ease;
    }

    .modal-close:active {
        background: rgba(239, 68, 68, 0.2);
        transform: scale(0.95);
    }

    /* Update service items layout for mobile */
    .service-item {
        display: flex;
        align-items: flex-start;
    }

    .service-item .icon-container {
        flex-shrink: 0;
    }

    /* Add pull-to-refresh style scroll indicator */
    .modal-body {
        position: relative;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .modal-body::-webkit-scrollbar {
        display: none;
    }

    .modal-body::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 2rem;
        background: linear-gradient(to top, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
        pointer-events: none;
    }
}

/* Update the service modals HTML structure */
.service-item {
    display: flex;
    align-items: flex-start;
    transition: all 0.3s ease;
}

.service-item .icon-container {
    flex-shrink: 0;
    margin-right: 1rem;
}

.service-item .content {
    flex: 1;
}

.service-item:active {
    transform: scale(0.98);
}

/* Leadership Cards Animation */
.founder-card {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.founder-card.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* Stagger animation for leadership cards */
.founder-card:nth-child(1) { transition-delay: 0s; }
.founder-card:nth-child(2) { transition-delay: 0.2s; }

.founder-card .founder-content {
    transform: translateY(20px);
    opacity: 0;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    transition-delay: 0.2s;
}

.founder-card.animate-in .founder-content {
    transform: translateY(0);
    opacity: 1;
}

.founder-card .founder-social-icons {
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transition-delay: 0.4s;
}

.founder-card.animate-in .founder-social-icons {
    opacity: 1;
    transform: translateY(0);
}

/* Value Cards Animation and Styling */
.value-card {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    background: linear-gradient(to bottom right, #ffffff, #f8fafc);
    border: 1px solid rgba(229, 231, 235, 0.5);
    border-radius: 1.5rem;
}

.value-card.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* Stagger animation for value cards */
.value-card:nth-child(1) { transition-delay: 0s; }
.value-card:nth-child(2) { transition-delay: 0.2s; }
.value-card:nth-child(3) { transition-delay: 0.4s; }

.value-card::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(
        45deg,
        transparent,
        rgba(239, 68, 68, 0.03) 40%,
        rgba(239, 68, 68, 0.05) 60%,
        transparent
    );
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.value-card:hover {
    transform: translateY(-8px);
    border-color: rgba(239, 68, 68, 0.2);
    box-shadow: 
        0 20px 40px -15px rgba(0, 0, 0, 0.1),
        0 0 20px rgba(239, 68, 68, 0.1);
}

.value-card:hover::before {
    opacity: 1;
}

.value-card .icon-container {
    position: relative;
    width: 4rem;
    height: 4rem;
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.05));
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    margin-bottom: 1.5rem;
}

.value-card:hover .icon-container {
    transform: scale(1.1) rotate(10deg);
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(239, 68, 68, 0.1));
}

.value-card .icon-container i {
    font-size: 1.75rem;
    color: rgb(239, 68, 68);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.value-card:hover .icon-container i {
    transform: scale(1.1) rotate(-10deg);
    color: rgb(220, 38, 38);
}

.value-card h3 {
    color: #1a1a1a;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.value-card:hover h3 {
    color: rgb(239, 68, 68);
    transform: translateX(5px);
}

.value-card p {
    color: #4b5563;
    font-size: 1rem;
    line-height: 1.6;
    transition: all 0.3s ease;
}

.value-card:hover p {
    color: #1f2937;
}

@media (max-width: 768px) {
    .value-card {
        margin-bottom: 1.5rem;
    }

    .value-card .icon-container {
        width: 3.5rem;
        height: 3.5rem;
    }

    .value-card .icon-container i {
        font-size: 1.5rem;
    }

    .value-card h3 {
        font-size: 1.25rem;
    }

    .value-card p {
        font-size: 0.875rem;
    }
}