<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['count' => 0]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['count' => 0]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div 
    x-data="{ 
        open: false, 
        unreadCount: <?php echo e($count); ?>,
        init() {
            // Make sure the badge is visible if we have notifications
            if (this.unreadCount > 0) {
                setTimeout(() => {
                    const badge = this.$el.querySelector('.notification-badge');
                    if (badge) {
                        badge.style.display = 'flex';
                        badge.textContent = this.unreadCount > 99 ? '99+' : this.unreadCount;
                    }
                }, 100);
            }
        },
        updateCount(count) {
            this.unreadCount = count;
            // Update badge visibility based on count
            const badge = this.$el.querySelector('.notification-badge');
            if (badge) {
                if (count > 0) {
                    badge.style.display = 'flex';
                    badge.textContent = count > 99 ? '99+' : count;
                } else {
                    badge.style.display = 'none';
                }
            }
        }
    }" 
    class="relative"
    @click.away="open = false"
    @notification-count-updated.window="updateCount($event.detail.count)"
>
    <button 
        @click="open = !open; if(open) loadNotifications()" 
        type="button" 
        class="relative p-2 text-gray-500 dark:text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-lg transition-all duration-300 hover:scale-110"
    >
        <!-- Bell Icon -->
        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
        </svg>
        
        <!-- Notification Badge -->
        <span 
            class="notification-badge absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full"
            style="display: none;"
            x-text="unreadCount > 99 ? '99+' : unreadCount"
            x-show="unreadCount > 0"
        ></span>
    </button>

    <!-- Dropdown -->
    <div 
        x-show="open"
        x-transition:enter="transition ease-out duration-200"
        x-transition:enter-start="opacity-0 scale-95"
        x-transition:enter-end="opacity-100 scale-100"
        x-transition:leave="transition ease-in duration-75"
        x-transition:leave-start="opacity-100 scale-100"
        x-transition:leave-end="opacity-0 scale-95"
        class="absolute z-[60] mt-2 w-80 rounded-md shadow-lg origin-top-right right-0"
        style="display: none;"
    >
        <div class="rounded-md ring-1 ring-black ring-opacity-5 py-1 bg-white dark:bg-gray-800">
            <div class="px-4 py-3 text-sm text-gray-900 dark:text-white border-b border-gray-100 dark:border-gray-700 flex justify-between items-center">
                <div class="font-medium">Notifications</div>
                <!-- <a href="#" class="text-sm text-blue-600 dark:text-blue-400 hover:underline mark-all-read">Mark all as read</a> -->
            </div>
            <div class="max-h-60 overflow-y-auto">
                <!-- Categories for notification types -->
                <div class="px-4 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700">
                    System Notifications
                </div>
                
                <!-- System notification items -->
                <div id="system-notifications" class="system-notifications-container"></div>
                
                <!-- Pending repairs section -->
                <div class="px-4 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600">
                    Pending Repairs
                </div>
                
                <!-- Pending repair items -->
                <div id="pending-repairs" class="pending-repairs-container"></div>
                
                <!-- In-progress repairs section -->
                <div class="px-4 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600">
                    In Progress Repairs
                </div>
                
                <!-- In-progress repair items -->
                <div id="in-progress-repairs" class="in-progress-repairs-container"></div>
                
                <!-- Unpaid repairs section -->
                <div class="px-4 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600">
                    Unpaid Repairs
                </div>
                
                <!-- Unpaid repair items -->
                <div id="unpaid-repairs" class="unpaid-repairs-container"></div>
                
                <!-- Default notification items if needed -->
                <div id="no-notifications">
                    <template x-if="unreadCount === 0">
                        <div class="px-4 py-3 text-sm text-gray-500 dark:text-gray-400">
                            No new notifications
                        </div>
                    </template>
                    
                    <?php echo e($slot); ?>

                </div>
            </div>
            <div class="px-4 py-2 text-sm border-t border-gray-100 dark:border-gray-700">
                <a href="<?php echo e(route('notifications.index')); ?>" class="text-blue-600 dark:text-blue-400 hover:underline block text-center py-1"
                   onclick="event.preventDefault();
                            window.notificationClickSource = 'bell'; 
                            if(window.location.pathname === '/notifications') {
                              window.dispatchEvent(new CustomEvent('notificationBellClicked')); 
                              open = false;
                            } else {
                              window.location.href = '<?php echo e(route('notifications.index')); ?>?source=bell';
                            }">
                    View all notifications
                </a>
            </div>
        </div>
    </div>
</div> <?php /**PATH C:\Users\<USER>\Desktop\Vsmart System\vsmart sms main\resources\views/components/notification-bell.blade.php ENDPATH**/ ?>