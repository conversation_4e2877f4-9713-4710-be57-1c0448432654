<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" class="h-full dark-transition" 
    x-data="{ 
        darkMode: localStorage.getItem('darkMode') === 'true',
        pageLoading: false,
        
        async navigate(url) {
            if (this.pageLoading) return;
            this.pageLoading = true;
            
            const content = document.querySelector('.page-content');
            
            try {
                const response = await fetch(url);
                const html = await response.text();
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');

                // Update content
                const newContent = doc.querySelector('.page-content');
                if (newContent) {
                    content.innerHTML = newContent.innerHTML;
                }

                // Update title
                const newTitle = doc.querySelector('title');
                if (newTitle) {
                    document.title = newTitle.textContent;
                }

                // Update URL
                window.history.pushState({}, '', url);

                // Update navigation active states
                this.updateNavigation(url);

                // First, execute any inline scripts that might define functions
                const inlineScripts = Array.from(doc.querySelectorAll('script:not([src])'));
                inlineScripts.forEach(script => {
                    try {
                        const newScript = document.createElement('script');
                        newScript.textContent = script.textContent;
                        document.body.appendChild(newScript);
                    } catch (err) {
                        console.error('Error executing inline script:', err);
                    }
                });

                // Then handle external scripts
                const externalScripts = Array.from(doc.querySelectorAll('script[src]'));
                await Promise.all(externalScripts.map(script => {
                    return new Promise((resolve, reject) => {
                        const newScript = document.createElement('script');
                        newScript.src = script.src;
                        newScript.onload = resolve;
                        newScript.onerror = reject;
                        document.body.appendChild(newScript);
                    });
                }));

                // Wait a brief moment for scripts to be ready
                await new Promise(resolve => setTimeout(resolve, 100));

                // Reinitialize Alpine.js components
                Alpine.initTree(content);

                // Reinitialize Select2
                if (window.jQuery && jQuery().select2) {
                    jQuery('.select2').select2({
                        width: '100%',
                        dropdownParent: document.body
                    });
                }

                // Handle modals initialization
                const modals = document.querySelectorAll('[x-data]');
                modals.forEach(modal => {
                    if (modal._x_dataStack) {
                        Alpine.initTree(modal);
                    }
                });

                // Reinitialize Chart.js
                const chartCanvas = document.getElementById('dailySalesChart');
                if (chartCanvas) {
                    // Destroy existing chart if it exists
                    const existingChart = Chart.getChart(chartCanvas);
                    if (existingChart) {
                        existingChart.destroy();
                    }
                    
                    // Wait a brief moment for Chart.js to be ready
                    setTimeout(() => {
                        // If the initialization function exists, call it
                        if (typeof window.initializeDailySalesChart === 'function') {
                            window.initializeDailySalesChart();
                        } else {
                            console.warn('Daily sales chart initialization function not found');
                        }
                    }, 100);
                }

                // Execute any stacked scripts
                const stackedScripts = doc.querySelector('#script-container');
                if (stackedScripts) {
                    const scriptContent = stackedScripts.textContent.trim();
                    if (scriptContent) {
                        const newScript = document.createElement('script');
                        newScript.textContent = scriptContent;
                        document.body.appendChild(newScript);
                    }
                }
                
                // Dispatch a custom event to let page scripts know navigation is complete
                document.dispatchEvent(new CustomEvent('page:loaded'));
                
                // Reinitialize customer page event handlers
                if (url.includes('/customers')) {
                    // Wait a tiny bit to ensure all scripts have loaded
                    setTimeout(() => {
                        reinitializeCustomerEventHandlers();
                        
                        // Ensure the key modal functions are available globally
                        if (typeof openModal === 'function') window.openModal = openModal;
                        if (typeof closeModal === 'function') window.closeModal = closeModal;
                        if (typeof showModal === 'function') window.showModal = showModal;
                        if (typeof hideModal === 'function') window.hideModal = hideModal;
                        
                        console.log('Customer handler reinitialization complete');
                    }, 200);
                }
                
                // Handle repair page event handlers
                if (url.includes('/repairs')) {
                    setTimeout(() => {
                        // Try both the global function and triggering a custom event
                        if (typeof initializeRepairEventHandlers === 'function') {
                            console.log('Calling initializeRepairEventHandlers directly');
                            initializeRepairEventHandlers();
                        }
                        
                        // Always dispatch the page:loaded event for event-based initialization
                        document.dispatchEvent(new CustomEvent('page:loaded', {
                            detail: { url: url, section: 'repairs' }
                        }));
                        
                        console.log('Repair handler reinitialization complete');
                    }, 200);
                }

            } catch (error) {
                console.error('Navigation error:', error);
                window.location.href = url;
            }

            this.pageLoading = false;
        },

        updateNavigation(url) {
            document.querySelectorAll('nav a').forEach(a => {
                if (a.getAttribute('href') === url) {
                    a.classList.remove('border-transparent', 'text-gray-500', 'dark:text-gray-400');
                    a.classList.add('border-blue-500', 'text-gray-900', 'dark:text-white');
                } else {
                    a.classList.remove('border-blue-500', 'text-gray-900', 'dark:text-white');
                    a.classList.add('border-transparent', 'text-gray-500', 'dark:text-gray-400');
                }
            });
        },

        mobileMenuOpen: false,
        toggleMenu() {
            this.mobileMenuOpen = !this.mobileMenuOpen;
            document.body.classList.toggle('menu-open', this.mobileMenuOpen);
        }
    }" 
    x-init="
        $watch('darkMode', val => localStorage.setItem('darkMode', val));
        
        // Handle navigation without page refresh
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a');
            if (link && link.href && link.href.startsWith(window.location.origin) && 
                !link.hasAttribute('download') && 
                !link.href.includes('/profile') && // Skip SPA navigation for profile pages
                !link.classList.contains('no-spa')) {
                e.preventDefault();
                navigate(link.href);
            }
        });

        // Handle browser back/forward
        window.addEventListener('popstate', () => {
            navigate(window.location.href);
        });

        // Handle form submissions
        document.addEventListener('submit', (e) => {
            const form = e.target;
            if (form.method.toLowerCase() === 'get') {
                e.preventDefault();
                const formData = new FormData(form);
                const queryString = new URLSearchParams(formData).toString();
                const url = form.action + (queryString ? '?' + queryString : '');
                navigate(url);
            }
        });
    "
    :class="{ 'dark': darkMode }">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

        <title>VSMART SMS</title>

        <!-- Favicon -->
        <link rel="icon" href="<?php echo e(asset('img/LogoClear.png')); ?>" type="image/png">

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Scripts -->
        <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
        <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
        
        <style>
            /* Dark mode transition */
            .dark-transition,
            .dark-transition * {
                transition: background-color 0.5s ease,
                            border-color 0.5s ease,
                            color 0.5s ease !important;
            }

            /* Hide elements with x-cloak */
            [x-cloak] {
                display: none !important;
            }

            /* Preserve button colors during transition */
            .dark-transition button.bg-blue-600 {
                transition: transform 0.3s ease,
                            opacity 0.3s ease !important;
            }

            /* Loading indicator */
            .loading-bar {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                height: 2px;
                background: linear-gradient(to right, #3b82f6, #60a5fa);
                transform-origin: 0;
                z-index: 50;
                opacity: 0.7;
            }

            /* Dark mode toggle animation */
            .theme-toggle-icon {
                transform-origin: center;
            }

            /* Page content */
            .page-content {
                position: relative;
                width: 100%;
            }

            /* Ensure content doesn't overflow */
            body {
                overflow-x: hidden;
            }
        </style>
        <?php echo $__env->yieldPushContent('styles'); ?>
    </head>
    <body class="font-sans antialiased h-full bg-gray-100 dark:bg-gray-900">
        <!-- Loading bar -->
        <div x-show="pageLoading" 
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="loading-bar"></div>

        <!-- Navigation Component -->
        <div class="min-h-screen bg-gray-100 dark:bg-gray-900" 
            x-data="{ 
                mobileMenuOpen: false,
                toggleMenu() {
                    this.mobileMenuOpen = !this.mobileMenuOpen;
                    document.body.classList.toggle('menu-open', this.mobileMenuOpen);
                }
            }">
            <!-- Top Navigation Bar -->
            <nav class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <!-- Left side -->
                        <div class="flex">
                            <!-- Logo -->
                            <div class="flex-shrink-0 flex items-center">
                                <a href="<?php echo e(route('dashboard')); ?>" class="text-xl font-bold text-red-600 dark:text-red-400">
                                    VS
                                </a>
                            </div>

                            <!-- Main Navigation -->
                            <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                                <a href="<?php echo e(route('dashboard')); ?>"
                                    class="<?php echo e(request()->routeIs('dashboard') ? 'border-blue-500 text-gray-900 dark:text-white' : 'border-transparent text-gray-500 dark:text-gray-400'); ?> hover:border-gray-300 dark:hover:border-gray-600 hover:text-gray-700 dark:hover:text-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-all duration-200">
                                    Dashboard
                                </a>
                                
                                <a href="<?php echo e(route('services.index')); ?>"
                                    class="<?php echo e(request()->routeIs('services.*') ? 'border-blue-500 text-gray-900 dark:text-white' : 'border-transparent text-gray-500 dark:text-gray-400'); ?> hover:border-gray-300 dark:hover:border-gray-600 hover:text-gray-700 dark:hover:text-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-all duration-200">
                                    Services
                                </a>

                                <a href="<?php echo e(route('customers.index')); ?>"
                                    class="<?php echo e(request()->routeIs('customers.*') ? 'border-blue-500 text-gray-900 dark:text-white' : 'border-transparent text-gray-500 dark:text-gray-400'); ?> hover:border-gray-300 dark:hover:border-gray-600 hover:text-gray-700 dark:hover:text-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-all duration-200">
                                    Customers
                                </a>

                                <a href="<?php echo e(route('repairs.index')); ?>"
                                    class="<?php echo e(request()->routeIs('repairs.*') ? 'border-blue-500 text-gray-900 dark:text-white' : 'border-transparent text-gray-500 dark:text-gray-400'); ?> hover:border-gray-300 dark:hover:border-gray-600 hover:text-gray-700 dark:hover:text-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-all duration-200">
                                    Repairs
                                </a>

                                <a href="<?php echo e(route('feedback.index')); ?>"
                                    class="<?php echo e(request()->routeIs('feedback.*') ? 'border-blue-500 text-gray-900 dark:text-white' : 'border-transparent text-gray-500 dark:text-gray-400'); ?> hover:border-gray-300 dark:hover:border-gray-600 hover:text-gray-700 dark:hover:text-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-all duration-200">
                                    Feedbacks
                                </a>

                                <a href="<?php echo e(route('inventory.index')); ?>"
                                    class="<?php echo e(request()->routeIs('inventory.*') ? 'border-blue-500 text-gray-900 dark:text-white' : 'border-transparent text-gray-500 dark:text-gray-400'); ?> hover:border-gray-300 dark:hover:border-gray-600 hover:text-gray-700 dark:hover:text-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-all duration-200">
                                    Inventory
                                </a>
                            </div>
                        </div>

                        <!-- Right side -->
                        <div class="hidden sm:ml-6 sm:flex sm:items-center space-x-4">
                            <!-- User dropdown -->
                            <?php echo $__env->make('layouts.navigation', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        </div>

                        <!-- Mobile menu button -->
                        <div class="flex items-center sm:hidden">
                            <button @click="toggleMenu()" 
                                class="mobile-menu-button inline-flex items-center justify-center p-2 rounded-md text-gray-500 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 transition duration-150 ease-in-out">
                                <svg class="h-6 w-6" :class="{'hidden': mobileMenuOpen, 'block': !mobileMenuOpen}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                                </svg>
                                <svg class="h-6 w-6" :class="{'block': mobileMenuOpen, 'hidden': !mobileMenuOpen}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Mobile menu -->
                <div 
                    x-show="mobileMenuOpen"
                    x-transition:enter="transition ease-out duration-200"
                    x-transition:enter-start="opacity-0 transform -translate-y-2"
                    x-transition:enter-end="opacity-100 transform translate-y-0"
                    x-transition:leave="transition ease-in duration-150"
                    x-transition:leave-start="opacity-100 transform translate-y-0"
                    x-transition:leave-end="opacity-0 transform -translate-y-2"
                    @click.away="mobileMenuOpen = false"
                    class="absolute top-16 inset-x-0 sm:hidden bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-lg z-50 max-h-[calc(100vh-4rem)] overflow-y-auto">
                    
                    <div class="pt-2 pb-3 space-y-1">
                        <a href="<?php echo e(route('dashboard')); ?>"
                            @click="mobileMenuOpen = false"
                            class="<?php echo e(request()->routeIs('dashboard') ? 'bg-blue-50 dark:bg-gray-700 border-blue-500 text-blue-700 dark:text-white' : 'border-transparent text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700'); ?> block pl-3 pr-4 py-2 border-l-4 text-base font-medium transition-all duration-200">
                            Dashboard
                        </a>
                        
                        <a href="<?php echo e(route('services.index')); ?>"
                            @click="mobileMenuOpen = false"
                            class="<?php echo e(request()->routeIs('services.*') ? 'bg-blue-50 dark:bg-gray-700 border-blue-500 text-blue-700 dark:text-white' : 'border-transparent text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700'); ?> block pl-3 pr-4 py-2 border-l-4 text-base font-medium transition-all duration-200">
                            Services
                        </a>
                        
                        <a href="<?php echo e(route('customers.index')); ?>"
                            @click="mobileMenuOpen = false"
                            class="<?php echo e(request()->routeIs('customers.*') ? 'bg-blue-50 dark:bg-gray-700 border-blue-500 text-blue-700 dark:text-white' : 'border-transparent text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700'); ?> block pl-3 pr-4 py-2 border-l-4 text-base font-medium transition-all duration-200">
                            Customers
                        </a>
                        
                        <a href="<?php echo e(route('repairs.index')); ?>"
                            @click="mobileMenuOpen = false"
                            class="<?php echo e(request()->routeIs('repairs.*') ? 'bg-blue-50 dark:bg-gray-700 border-blue-500 text-blue-700 dark:text-white' : 'border-transparent text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700'); ?> block pl-3 pr-4 py-2 border-l-4 text-base font-medium transition-all duration-200">
                            Repairs
                        </a>
                        
                        <a href="<?php echo e(route('feedback.index')); ?>"
                            @click="mobileMenuOpen = false"
                            class="<?php echo e(request()->routeIs('feedback.*') ? 'bg-blue-50 dark:bg-gray-700 border-blue-500 text-blue-700 dark:text-white' : 'border-transparent text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700'); ?> block pl-3 pr-4 py-2 border-l-4 text-base font-medium transition-all duration-200">
                            Feedbacks
                        </a>
                        
                        <a href="<?php echo e(route('inventory.index')); ?>"
                            @click="mobileMenuOpen = false"
                            class="<?php echo e(request()->routeIs('inventory.*') ? 'bg-blue-50 dark:bg-gray-700 border-blue-500 text-blue-700 dark:text-white' : 'border-transparent text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700'); ?> block pl-3 pr-4 py-2 border-l-4 text-base font-medium transition-all duration-200">
                            Inventory
                        </a>
                    </div>

                    <!-- Mobile user menu -->
                    <div class="pt-4 pb-3 border-t border-gray-200 dark:border-gray-700">
                        <div class="flex items-center px-4">
                            <div class="flex-shrink-0">
                                <div class="h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                                    <span class="text-lg font-medium text-gray-700 dark:text-gray-200">
                                        <?php echo e(substr(Auth::user()->name, 0, 1)); ?>

                                    </span>
                                </div>
                            </div>
                            <div class="ml-3">
                                <div class="text-base font-medium text-gray-800 dark:text-gray-200"><?php echo e(Auth::user()->name); ?></div>
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400"><?php echo e(Auth::user()->email); ?></div>
                            </div>
                        </div>
                        <div class="mt-3 space-y-1">
                            <a href="<?php echo e(route('profile.show')); ?>"
                                @click="mobileMenuOpen = false"
                                class="block px-4 py-2 text-base font-medium text-gray-500 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200">
                                Profile
                            </a>
                            <form method="POST" action="<?php echo e(route('logout')); ?>">
                                <?php echo csrf_field(); ?>
                                <button type="submit"
                                    @click="mobileMenuOpen = false"
                                    class="block w-full text-left px-4 py-2 text-base font-medium text-gray-500 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200">
                                    Log Out
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Page Content -->
            <main class="py-6">
                <div class="page-content max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <?php echo $__env->yieldContent('content'); ?>
                </div>
            </main>
        </div>

        <?php echo $__env->yieldPushContent('scripts'); ?>
        <div id="script-container" style="display: none;">
            <?php echo $__env->yieldPushContent('scripts'); ?>
        </div>
        
        <script>
            // Create Alpine.js store for dark mode
            document.addEventListener('alpine:init', () => {
                Alpine.store('darkMode', {
                    dark: localStorage.getItem('darkMode') === 'true',
                    
                    toggle() {
                        this.dark = !this.dark;
                        localStorage.setItem('darkMode', this.dark);
                        
                        if (this.dark) {
                            document.documentElement.classList.add('dark');
                            document.getElementById('darkModeText').textContent = 'Light Mode';
                            document.getElementById('darkModeTextMobile').textContent = 'Light Mode';
                            document.querySelector('.dark-mode-light')?.classList.add('hidden');
                            document.querySelector('.dark-mode-dark')?.classList.remove('hidden');
                        } else {
                            document.documentElement.classList.remove('dark');
                            document.getElementById('darkModeText').textContent = 'Dark Mode';
                            document.getElementById('darkModeTextMobile').textContent = 'Dark Mode';
                            document.querySelector('.dark-mode-light')?.classList.remove('hidden');
                            document.querySelector('.dark-mode-dark')?.classList.add('hidden');
                        }
                    },
                    
                    init() {
                        if (this.dark) {
                            document.documentElement.classList.add('dark');
                        } else {
                            document.documentElement.classList.remove('dark');
                        }
                    }
                });
            });

            // Function to reinitialize all customer page event handlers
            function reinitializeCustomerEventHandlers() {
                console.log('Reinitializing customer event handlers');
                
                // Make sure the modal functions are available
                if (typeof showModal === 'function') {
                    window.showModal = showModal;
                }
                
                if (typeof hideModal === 'function') {
                    window.hideModal = hideModal;
                }
                
                if (typeof openModal === 'function') {
                    window.openModal = openModal;
                    
                    // Add New Customer button
                    const addNewCustomerBtn = document.querySelector('button[onclick="openModal()"]');
                    if (addNewCustomerBtn) {
                        addNewCustomerBtn.onclick = function() {
                            openModal();
                        };
                    }
                }
                
                // View customer buttons
                const viewButtons = document.querySelectorAll('button[onclick^="openViewModal"]');
                viewButtons.forEach(button => {
                    const customerId = button.getAttribute('data-customer-id');
                    button.onclick = function() {
                        if (typeof openViewModal === 'function') {
                            openViewModal(customerId);
                        } else {
                            console.error('openViewModal function not found');
                        }
                    };
                });
                
                // Other buttons with onclick handlers
                document.querySelectorAll('button[onclick]').forEach(button => {
                    const onclickValue = button.getAttribute('onclick');
                    if (onclickValue && !button.customHandlerAttached) {
                        const handlerName = onclickValue.split('(')[0];
                        const params = onclickValue.match(/\((.*)\)/);
                        
                        button.customHandlerAttached = true;
                        button.addEventListener('click', function(e) {
                            e.preventDefault();
                            
                            if (typeof window[handlerName] === 'function') {
                                // For buttons that use dataset attributes
                                if (onclickValue.includes('this.dataset')) {
                                    const customerId = this.getAttribute('data-customer-id');
                                    window[handlerName](customerId);
                                } else {
                                    // Try to execute the original function
                                    try {
                                        // Extract parameters and execute safely
                                        const paramValue = params ? params[1].replace(/['"]/g, '') : '';
                                        if (paramValue) {
                                            window[handlerName](paramValue);
                                        } else {
                                            window[handlerName]();
                                        }
                                    } catch (err) {
                                        console.error('Error executing handler:', err);
                                    }
                                }
                            }
                        });
                    }
                });
                
                // Forms with custom handlers
                const deleteForms = document.querySelectorAll('.delete-customer-form');
                deleteForms.forEach(form => {
                    if (!form.hasSubmitListener) {
                        form.hasSubmitListener = true;
                        form.addEventListener('submit', function(e) {
                            e.preventDefault();
                            if (confirm('Are you sure you want to delete this customer?')) {
                                form.submit();
                            }
                        });
                    }
                });
            }

            // Call when page first loads
            document.addEventListener('DOMContentLoaded', function() {
                if (window.location.pathname.includes('/customers')) {
                    reinitializeCustomerEventHandlers();
                }
            });

            // Also reinitialize on SPA navigation completion
            document.addEventListener('page:loaded', function() {
                if (window.location.pathname.includes('/customers')) {
                    reinitializeCustomerEventHandlers();
                }
            });

            // Function to reinitialize repair page event handlers
            function reinitializeRepairEventHandlers() {
                // Check if there are repair-specific functions that were defined
                if (typeof window.initializeRepairEventHandlers === 'function') {
                    console.log('Found initializeRepairEventHandlers in window object, calling it');
                    window.initializeRepairEventHandlers();
                } else {
                    console.log('initializeRepairEventHandlers not found in window object');
                    
                    // Dispatch a custom event that repair pages can listen for
                    document.dispatchEvent(new CustomEvent('repair:reinitialize'));
                    
                    // Check if we're on a repair page by looking for repair elements
                    const paymentModal = document.getElementById('payment-modal');
                    const paymentButtons = document.querySelectorAll('.payment-button');
                    
                    if (paymentModal || paymentButtons.length > 0) {
                        console.log('Repair elements found but no handler function, adding page reload fallback');
                        // If elements exist but no handler, we may need to reload the page
                        const reloadNeeded = !window.repairHandlersInitialized;
                        
                        if (reloadNeeded) {
                            console.log('Reloading page to reinitialize repair handlers');
                            window.location.reload();
                        }
                    }
                }
                
                // Mark that we've attempted initialization
                window.repairHandlersInitialized = true;
            }

            // Ensure Alpine.js is properly initialized
            document.addEventListener('DOMContentLoaded', function() {
                // Initialize Alpine.js if needed
                if (typeof Alpine !== 'undefined' && !window.alpineInitialized) {
                    Alpine.start();
                    window.alpineInitialized = true;
                    
                    // Force Alpine.js to reinitialize all components
                    document.querySelectorAll('[x-data]').forEach(el => {
                        if (el._x_dataStack) {
                            el._x_dataStack.forEach(item => {
                                if (typeof item === 'object') {
                                    // Ensure all Alpine.js states are properly reset
                                    Object.keys(item).forEach(key => {
                                        if (key.startsWith('is') && typeof item[key] === 'boolean') {
                                            item[key] = false;
                                        }
                                    });
                                }
                            });
                        }
                    });
                }
            });

            // Listen for navigation events and reinitialize Alpine components
            document.addEventListener('turbo:load', function() {
                if (typeof Alpine !== 'undefined') {
                    Alpine.start();
                }
            });

            // Notifications handling
            document.addEventListener('DOMContentLoaded', function() {
                // Initial load of notification counts
                fetchNotificationCounts();
                
                // Set up interval to refresh notification counts every 15 seconds
                setInterval(fetchNotificationCounts, 15000);
                
                // Add event listener for the mark all as read link
                document.addEventListener('click', function(e) {
                    if (e.target.matches('.mark-all-read') || e.target.closest('.mark-all-read')) {
                        e.preventDefault();
                        markAllNotificationsAsRead();
                    }
                });
                
                // Force an immediate refresh of notification count when page loads
                setTimeout(fetchNotificationCounts, 1000);
            });
            
            function fetchNotificationCounts() {
                fetch('/notifications/counts')
                    .then(response => response.json())
                    .then(data => {
                        console.log('Fetched notification counts:', data);
                        
                        // Calculate the total if needed (though it should be provided by the server now)
                        const totalCount = data.total_count || 
                                         (data.unread_count + 
                                          data.pending_repairs_count + 
                                          data.in_progress_repairs_count + 
                                          data.unpaid_repairs_count);
                        
                        // Dispatch a custom event that the notification bell can listen for
                        window.dispatchEvent(new CustomEvent('notification-count-updated', {
                            detail: { count: totalCount }
                        }));
                        
                        // Update notification count directly in Alpine component
                        const bellComponent = document.getElementById('notification-bell');
                        if (bellComponent && bellComponent.__x) {
                            bellComponent.__x.$data.unreadCount = totalCount;
                            
                            // Make sure the badge is visible when count > 0
                            const badge = bellComponent.querySelector('.notification-badge');
                            if (badge) {
                                if (totalCount > 0) {
                                    badge.style.display = 'flex';
                                    badge.textContent = totalCount > 99 ? '99+' : totalCount;
                                } else {
                                    badge.style.display = 'none';
                                }
                            }
                        }
                    })
                    .catch(error => console.error('Error fetching notification counts:', error));
            }
            
            function loadNotifications() {
                fetch('/notifications/data')
                    .then(response => response.json())
                    .then(data => {
                        // Handle system notifications
                        const systemNotificationsContainer = document.getElementById('system-notifications');
                        systemNotificationsContainer.innerHTML = '';
                        
                        if (data.notifications.length === 0) {
                            systemNotificationsContainer.innerHTML = `
                                <div class="px-4 py-3 text-center text-sm text-gray-500 dark:text-gray-400">
                                    No new notifications
                                </div>
                            `;
                        } else {
                            data.notifications.forEach(notification => {
                                const element = createNotificationElement(notification);
                                systemNotificationsContainer.appendChild(element);
                            });
                        }
                        
                        // Handle pending repairs
                        const pendingRepairsContainer = document.getElementById('pending-repairs');
                        if (pendingRepairsContainer) {
                            pendingRepairsContainer.innerHTML = '';
                            
                            if (data.pending_repairs.length === 0) {
                                pendingRepairsContainer.innerHTML = `
                                    <div class="px-4 py-3 text-center text-sm text-gray-500 dark:text-gray-400">
                                        No pending repairs
                                    </div>
                                `;
                            } else {
                                data.pending_repairs.forEach(repair => {
                                    const element = createRepairNotificationElement(repair, 'pending');
                                    pendingRepairsContainer.appendChild(element);
                                });
                            }
                        }
                        
                        // Handle in-progress repairs
                        const inProgressRepairsContainer = document.getElementById('in-progress-repairs');
                        if (inProgressRepairsContainer) {
                            inProgressRepairsContainer.innerHTML = '';
                            
                            if (data.in_progress_repairs.length === 0) {
                                inProgressRepairsContainer.innerHTML = `
                                    <div class="px-4 py-3 text-center text-sm text-gray-500 dark:text-gray-400">
                                        No in-progress repairs
                                    </div>
                                `;
                            } else {
                                data.in_progress_repairs.forEach(repair => {
                                    const element = createRepairNotificationElement(repair, 'in_progress');
                                    inProgressRepairsContainer.appendChild(element);
                                });
                            }
                        }
                        
                        // Handle unpaid repairs
                        const unpaidRepairsContainer = document.getElementById('unpaid-repairs');
                        unpaidRepairsContainer.innerHTML = '';
                        
                        if (data.unpaid_repairs.length === 0) {
                            unpaidRepairsContainer.innerHTML = `
                                <div class="px-4 py-3 text-center text-sm text-gray-500 dark:text-gray-400">
                                    No unpaid repairs
                                </div>
                            `;
                        } else {
                            data.unpaid_repairs.forEach(repair => {
                                const element = createRepairNotificationElement(repair, 'unpaid');
                                unpaidRepairsContainer.appendChild(element);
                            });
                        }
                        
                        // Update count
                        const totalCount = data.notifications.length + 
                                          data.pending_repairs.length + 
                                          data.in_progress_repairs.length +
                                          data.unpaid_repairs.length;
                        
                        const countBadge = document.getElementById('notification-count');
                        if (totalCount > 0) {
                            countBadge.textContent = totalCount > 99 ? '99+' : totalCount;
                            countBadge.classList.remove('hidden');
                        } else {
                            countBadge.classList.add('hidden');
                        }
                        
                        // Show or hide "no notifications" message
                        const noNotificationsMsg = document.getElementById('no-notifications');
                        if (totalCount > 0) {
                            noNotificationsMsg.classList.add('hidden');
                        } else {
                            noNotificationsMsg.classList.remove('hidden');
                        }
                    })
                    .catch(error => console.error('Error fetching notifications:', error));
            }
            
            function createNotificationElement(notification) {
                const div = document.createElement('div');
                div.className = 'px-4 py-3 border-b border-gray-100 dark:border-gray-700 ' + 
                                (notification.read_at ? 'bg-white dark:bg-gray-800' : 'bg-blue-50 dark:bg-blue-900');
                
                const data = notification.data;
                
                // Create content based on notification type
                if (notification.type.includes('NewFeedbackNotification')) {
                    if (data.type === 'unfeatured_feedback') {
                        // Unfeatured feedback notification
                        div.innerHTML = `
                            <div class="flex items-start">
                                <div class="flex-shrink-0 mr-3">
                                    <svg class="h-5 w-5 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">Unfeatured Feedback Reminder</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">
                                        You have ${data.count || 0} unfeatured feedback${(data.count || 0) > 1 ? 's' : ''} waiting for review
                                    </p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                        <a href="/feedback" class="text-blue-600 dark:text-blue-400 hover:underline">
                                            Review Feedback
                                        </a>
                                    </p>
                                </div>
                            </div>
                        `;
                    } else if (data.type === 'sales_report_reminder') {
                        // Sales report reminder notification
                        div.innerHTML = `
                            <div class="flex items-start">
                                <div class="flex-shrink-0 mr-3">
                                    <svg class="h-5 w-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M3 3a1 1 0 000 2h10a1 1 0 100-2H3zm0 4a1 1 0 000 2h10a1 1 0 100-2H3zm0 4a1 1 0 100 2h10a1 1 0 100-2H3zm12-7a1 1 0 10-2 0v6a1 1 0 102 0V4z" />
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">${data.period === 'weekly' ? 'Weekly' : 'Monthly'} Sales Report</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">
                                        ${data.message || 'Sales report is now available'}
                                    </p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                        <a href="${data.report_url || '/reports/generate'}" class="text-blue-600 dark:text-blue-400 hover:underline">
                                            View Report
                                        </a>
                                    </p>
                                </div>
                            </div>
                        `;
                    } else if (data.type === 'shared_feedback') {
                        // Shared feedback notification
                        div.innerHTML = `
                            <div class="flex items-start">
                                <div class="flex-shrink-0 mr-3">
                                    <svg class="h-5 w-5 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 5v8a2 2 0 01-2 2h-5l-5 4v-4H4a2 2 0 01-2-2V5a2 2 0 012-2h12a2 2 0 012 2zM7 8H5v2h2V8zm2 0h2v2H9V8zm6 0h-2v2h2V8z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">Shared Feedback</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">
                                        ${data.shared_by || 'Someone'} shared feedback from ${data.name || 'a customer'} (${data.rating || '?'}/5)
                                    </p>
                                    ${data.shared_message ? `
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                        <strong>Note:</strong> "${data.shared_message}"
                                    </p>
                                    ` : ''}
                                    ${data.message ? `
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                        <strong>Feedback:</strong> "${data.message.substring(0, 100)}${data.message.length > 100 ? '...' : ''}"
                                    </p>
                                    ` : ''}
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                        <a href="/feedback/${data.id}/share" class="text-blue-600 dark:text-blue-400 hover:underline">
                                            View Full Feedback
                                        </a>
                                    </p>
                                </div>
                            </div>
                        `;
                    } else {
                        // Regular feedback notification
                        div.innerHTML = `
                            <div class="flex items-start">
                                <div class="flex-shrink-0 mr-3">
                                    <svg class="h-5 w-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 5v8a2 2 0 01-2 2h-5l-5 4v-4H4a2 2 0 01-2-2V5a2 2 0 012-2h12a2 2 0 012 2zM7 8H5v2h2V8zm2 0h2v2H9V8zm6 0h-2v2h2V8z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">New Feedback</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">
                                        ${data.name || 'Customer'} left a ${data.rating || '?'}/5 rating
                                    </p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">${new Date(data.created_at).toLocaleString()}</p>
                                </div>
                            </div>
                        `;
                    }
                } else {
                    // Default notification
                    div.innerHTML = `
                        <div class="flex items-start">
                            <div class="flex-shrink-0 mr-3">
                                <svg class="h-5 w-5 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-900 dark:text-white">System Notification</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">
                                    You have a new notification
                                </p>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">${new Date(notification.created_at).toLocaleString()}</p>
                            </div>
                        </div>
                    `;
                }
                
                return div;
            }
            
            function createRepairNotificationElement(repair, status) {
                const div = document.createElement('div');
                div.className = 'px-4 py-3 border-b border-gray-100 dark:border-gray-700 ' + 
                                (status === 'pending' ? 'bg-yellow-50 dark:bg-yellow-900/20' : 
                                 status === 'in_progress' ? 'bg-blue-50 dark:bg-blue-900/20' : 
                                 status === 'unpaid' ? 'bg-red-50 dark:bg-red-900/20' : 'bg-gray-50 dark:bg-gray-900/20');
                
                const statusText = status === 'pending' ? 'Pending Repair' : 
                                   status === 'in_progress' ? 'In Progress Repair' : 
                                   status === 'unpaid' ? 'Unpaid Repair' : 'Repair';
                
                const iconClass = status === 'pending' ? 'text-yellow-500' : 
                                  status === 'in_progress' ? 'text-blue-500' : 
                                  status === 'unpaid' ? 'text-red-500' : 'text-gray-500';
                
                let iconPath;
                
                if (status === 'pending') {
                    iconPath = '<path fill-rule="evenodd" d="M10 2a8 8 0 100 16 8 8 0 000-16zm0 14a6 6 0 110-12 6 6 0 010 12zm-1-5a1 1 0 011-1h2a1 1 0 110 2h-2a1 1 0 01-1-1zm1-7a1 1 0 100 2 1 1 0 000-2z" clip-rule="evenodd" />';
                } else if (status === 'in_progress') {
                    iconPath = '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />';
                } else if (status === 'unpaid') {
                    iconPath = '<path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />';
                } else {
                    iconPath = '<path fill-rule="evenodd" d="M10 2a8 8 0 100 16 8 8 0 000-16zm0 14a6 6 0 110-12 6 6 0 010 12zm-1-5a1 1 0 011-1h2a1 1 0 110 2h-2a1 1 0 01-1-1zm1-7a1 1 0 100 2 1 1 0 000-2z" clip-rule="evenodd" />';
                }
                
                // Get the first item details
                const firstItem = repair.items[0];
                const deviceName = firstItem ? `${firstItem.device.brand} ${firstItem.device.model}` : 'Unknown device';
                const customerName = firstItem && firstItem.device.customer ? firstItem.device.customer.name : 'Unknown customer';
                
                // Additional info for unpaid repairs
                let additionalInfo = '';
                if (status === 'unpaid' && repair.total_cost) {
                    additionalInfo = `<span class="text-red-600 dark:text-red-400 font-medium"> • ₱${parseFloat(repair.total_cost).toFixed(2)}</span>`;
                }
                
                div.innerHTML = `
                    <div class="flex items-start">
                        <div class="flex-shrink-0 mr-2">
                            <svg class="h-4 w-4 ${iconClass}" fill="currentColor" viewBox="0 0 20 20">
                                ${iconPath}
                            </svg>
                        </div>
                        <div class="flex-grow text-xs">
                            <p class="text-gray-900 dark:text-white font-medium">
                                ${statusText}${additionalInfo}
                            </p>
                            <p class="text-gray-500 dark:text-gray-400 truncate">
                                ${deviceName} for ${customerName}
                            </p>
                            <p class="text-gray-500 dark:text-gray-400 mt-1">
                                <a href="/repairs/${repair.id}" class="text-blue-600 dark:text-blue-400 hover:underline">
                                    View details
                                </a>
                            </p>
                        </div>
                    </div>
                `;
                
                return div;
            }
            
            function markAllNotificationsAsRead() {
                fetch('/notifications/mark-all-read', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    console.log('Marked all notifications as read:', data);
                    
                    // Refresh notification counts
                    fetchNotificationCounts();
                    
                    // Refresh notification content if the dropdown is open
                    const bellComponent = document.getElementById('notification-bell');
                    if (bellComponent && bellComponent.__x && bellComponent.__x.$data.open) {
                        loadNotifications();
                    }
                    
                    // If we're on the notifications page, dispatch a custom event
                    if (window.location.pathname.includes('/notifications')) {
                        window.dispatchEvent(new CustomEvent('notificationBellClicked'));
                    }
                })
                .catch(error => console.error('Error marking notifications as read:', error));
            }
        </script>

        <!-- Add backdrop overlay -->
        <div 
            x-show="mobileMenuOpen" 
            x-transition:enter="transition ease-out duration-200"
            x-transition:enter-start="opacity-0"
            x-transition:enter-end="opacity-100"
            x-transition:leave="transition ease-in duration-150"
            x-transition:leave-start="opacity-100"
            x-transition:leave-end="opacity-0"
            @click="mobileMenuOpen = false"
            class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-40 sm:hidden"
        ></div>
    </body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\Vsmart System\vsmart sms main\resources\views/layouts/app.blade.php ENDPATH**/ ?>