<?php $__env->startSection('content'); ?>
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Welcome Section -->
        <div class="bg-gradient-to-r from-blue-600 to-blue-800 dark:from-blue-800 dark:to-blue-900 rounded-xl shadow-xl p-8 mb-8">
            <div class="flex justify-between items-center">
                <div class="space-y-2">
                    <h2 class="text-3xl font-bold text-white">Welcome back, <?php echo e(Auth::user()->name); ?>!</h2>
                    <p class="text-blue-100 text-lg">Here's an overview of your business today.</p>
                </div>
                <div class="text-blue-100 text-lg font-medium bg-blue-500/20 px-4 py-2 rounded-lg backdrop-blur-sm">
                    <?php echo e(now()->format('l, F j, Y')); ?>

                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="mb-8">
            <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4 px-1">Quick Actions</h3>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <a href="<?php echo e(route('repairs.create')); ?>" 
                   class="group bg-gradient-to-br from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 p-6 rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                    <div class="flex items-center">
                        <div class="bg-white/20 rounded-lg p-3 mr-4">
                            <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                            </svg>
                        </div>
                        <div>
                            <span class="block text-lg font-semibold text-white">New Repair</span>
                            <span class="text-blue-100 text-sm">Create repair ticket</span>
                        </div>
                    </div>
                </a>

                <a href="<?php echo e(route('customers.index')); ?>" 
                   class="group bg-gradient-to-br from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 p-6 rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                    <div class="flex items-center">
                        <div class="bg-white/20 rounded-lg p-3 mr-4">
                            <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                        </div>
                        <div>
                            <span class="block text-lg font-semibold text-white">Customers</span>
                            <span class="text-yellow-100 text-sm">Manage customers</span>
                        </div>
                    </div>
                </a>

                <a href="<?php echo e(route('reports.generate')); ?>" 
                   class="group bg-gradient-to-br from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 p-6 rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                    <div class="flex items-center">
                        <div class="bg-white/20 rounded-lg p-3 mr-4">
                            <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                        </div>
                        <div>
                            <span class="block text-lg font-semibold text-white">Reports</span>
                            <span class="text-purple-100 text-sm">Generate reports</span>
                        </div>
                    </div>
                </a>

                <a href="<?php echo e(route('profile.show')); ?>" 
                   class="group bg-gradient-to-br from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 p-6 rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                    <div class="flex items-center">
                        <div class="bg-white/20 rounded-lg p-3 mr-4">
                            <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                            </svg>
                        </div>
                        <div>
                            <span class="block text-lg font-semibold text-white">Profile</span>
                            <span class="text-emerald-100 text-sm">Manage account</span>
                        </div>
                    </div>
                </a>
            </div>
        </div>

        <!-- Stats Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Pending Repairs -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="bg-amber-100 dark:bg-amber-900/50 rounded-lg p-3">
                            <svg class="h-6 w-6 text-amber-600 dark:text-amber-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <span class="text-xs font-medium text-amber-600 dark:text-amber-400 bg-amber-100 dark:bg-amber-900/50 px-2.5 py-1 rounded-full">Pending</span>
                    </div>
                    <div class="flex flex-col">
                        <div class="flex items-baseline">
                            <p class="text-2xl font-bold text-gray-900 dark:text-white"><?php echo e($pendingRepairs ?? 0); ?></p>
                            <p class="ml-2 text-sm text-gray-500 dark:text-gray-400">repairs</p>
                        </div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-300 mt-1">Total Pending Repairs</p>
                    </div>
                </div>
            </div>

            <!-- Total Customers -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="bg-green-100 dark:bg-green-900/50 rounded-lg p-3">
                            <svg class="h-6 w-6 text-green-600 dark:text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                        </div>
                        <span class="text-xs font-medium text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/50 px-2.5 py-1 rounded-full">Active</span>
                    </div>
                    <div class="flex flex-col">
                        <div class="flex items-baseline">
                            <p class="text-2xl font-bold text-gray-900 dark:text-white"><?php echo e($totalCustomers ?? 0); ?></p>
                            <p class="ml-2 text-sm text-gray-500 dark:text-gray-400">customers</p>
                        </div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-300 mt-1">Total Customers</p>
                    </div>
                </div>
            </div>

            <!-- Active Services -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="bg-purple-100 dark:bg-purple-900/50 rounded-lg p-3">
                            <svg class="h-6 w-6 text-purple-600 dark:text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                            </svg>
                        </div>
                        <span class="text-xs font-medium text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-900/50 px-2.5 py-1 rounded-full">Services</span>
                    </div>
                    <div class="flex flex-col">
                        <div class="flex items-baseline">
                            <p class="text-2xl font-bold text-gray-900 dark:text-white"><?php echo e($activeServices ?? 0); ?></p>
                            <p class="ml-2 text-sm text-gray-500 dark:text-gray-400">active</p>
                        </div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-300 mt-1">Active Services</p>
                    </div>
                </div>
            </div>

            <!-- Unpaid Service -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="bg-red-100 dark:bg-red-900/50 rounded-lg p-3">
                        <svg class="h-6 w-6 text-red-600 dark:text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                        </div>
                        <span class="text-xs font-medium text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/50 px-2.5 py-1 rounded-full">Alert</span>
                    </div>
                    <div class="flex flex-col">
                        <div class="flex items-baseline">
                            <p class="text-2xl font-bold text-gray-900 dark:text-white"><?php echo e($unpaidRepairsCount ?? 0); ?></p>
                            <p class="ml-2 text-sm text-gray-500 dark:text-gray-400">repairs</p>
                        </div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-300 mt-1">
                            <a href="<?php echo e(route('repairs.index', ['filter' => 'unpaid'])); ?>" class="hover:text-red-600 dark:hover:text-red-400 transition-colors duration-200">
                                Unpaid Service
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sales Statistics -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <!-- Weekly Sales -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-lg font-bold text-gray-900 dark:text-white">Weekly Sales</h2>
                    <select id="weekSelect" class="text-sm border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <?php for($i = 0; $i < 4; $i++): ?>
                            <option value="<?php echo e($i); ?>" <?php echo e(request('week', 0) == $i ? 'selected' : ''); ?>>
                                <?php echo e($i == 0 ? 'This Week' : ($i == 1 ? 'Last Week' : $i . ' Weeks Ago')); ?>

                            </option>
                        <?php endfor; ?>
                    </select>
                </div>
                <div class="flex items-baseline space-x-2">
                    <p class="text-3xl font-bold text-blue-600 dark:text-blue-400" id="weeklyAmount">₱<?php echo e(number_format($weeklySales ?? 0, 2)); ?></p>
                    <p class="text-sm text-gray-500 dark:text-gray-400" id="weekRange"><?php echo e($weekStart->format('M d') . ' - ' . $weekEnd->format('M d')); ?></p>
                </div>
            </div>

            <!-- Monthly Sales -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-lg font-bold text-gray-900 dark:text-white">Monthly Sales</h2>
                    <select id="monthSelect" class="text-sm border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <?php for($i = 0; $i < 12; $i++): ?>
                            <?php
                                $date = now()->subMonths($i);
                                $monthValue = $date->format('Y-m');
                            ?>
                            <option value="<?php echo e($monthValue); ?>" <?php echo e(request('month', now()->format('Y-m')) == $monthValue ? 'selected' : ''); ?>>
                                <?php echo e($date->format('F Y')); ?>

                            </option>
                        <?php endfor; ?>
                    </select>
                </div>
                <div class="flex items-baseline space-x-2">
                    <p class="text-3xl font-bold text-blue-600 dark:text-blue-400" id="monthlyAmount">₱<?php echo e(number_format($monthlySales ?? 0, 2)); ?></p>
                    <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo e($selectedMonth ?? now()->format('F Y')); ?></p>
                </div>
            </div>

            <!-- Yearly Sales -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-lg font-bold text-gray-900 dark:text-white">Yearly Sales</h2>
                    <select id="yearSelect" class="text-sm border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <?php for($i = 0; $i < 5; $i++): ?>
                            <?php
                                $year = now()->subYears($i)->year;
                            ?>
                            <option value="<?php echo e($year); ?>" <?php echo e(request('year', now()->year) == $year ? 'selected' : ''); ?>><?php echo e($year); ?></option>
                        <?php endfor; ?>
                    </select>
                </div>
                <div class="flex items-baseline space-x-2">
                    <p class="text-3xl font-bold text-blue-600 dark:text-blue-400" id="yearlyAmount">₱<?php echo e(number_format($yearlySales ?? 0, 2)); ?></p>
                    <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo e($selectedYear ?? now()->year); ?></p>
                </div>
            </div>
        </div>

        <!-- Sales Chart -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <h2 class="text-lg font-bold text-gray-900 dark:text-white mb-6">Daily Sales - Last 7 Days</h2>
            <div class="h-80">
                <canvas id="dailySalesChart" data-sales='<?php echo e(json_encode($salesData ?? [])); ?>'></canvas>
            </div>
        </div>
    </div>

    <?php $__env->startPush('scripts'); ?>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        initializeChart();
        
        // Function to update sales data
        function updateSalesData(period, value) {
            // Build query parameters including all current selections
            const params = new URLSearchParams();
            params.set(period, value);
            
            // Add other selections to maintain state
            if (period !== 'week') params.set('week', document.querySelector('#weekSelect').value);
            if (period !== 'month') params.set('month', document.querySelector('#monthSelect').value);
            if (period !== 'year') params.set('year', document.querySelector('#yearSelect').value);

            fetch(`/dashboard/sales-data?${params.toString()}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    console.error('Error fetching sales data:', data.error);
                    return;
                }
                
                // Update the sales displays
                if (period === 'week' || data.weeklySales) {
                    document.querySelector('#weeklyAmount').textContent = '₱' + data.weeklySales;
                    document.querySelector('#weekRange').textContent = data.weekRange;
                }
                if (period === 'month' || data.monthlySales) {
                    document.querySelector('#monthlyAmount').textContent = '₱' + data.monthlySales;
                }
                if (period === 'year' || data.yearlySales) {
                    document.querySelector('#yearlyAmount').textContent = '₱' + data.yearlySales;
                }

                // Update URL without page reload to maintain state
                const url = new URL(window.location);
                url.searchParams.set(period, value);
                window.history.pushState({}, '', url);
            })
            .catch(error => {
                console.error('Error:', error);
            });
        }

        // Add event listeners to the dropdowns
        document.querySelector('#weekSelect').addEventListener('change', function(e) {
            e.preventDefault();
            updateSalesData('week', this.value);
        });

        document.querySelector('#monthSelect').addEventListener('change', function(e) {
            e.preventDefault();
            updateSalesData('month', this.value);
        });

        document.querySelector('#yearSelect').addEventListener('change', function(e) {
            e.preventDefault();
            updateSalesData('year', this.value);
        });

        // Initialize with current URL parameters if they exist
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.has('week')) updateSalesData('week', urlParams.get('week'));
        if (urlParams.has('month')) updateSalesData('month', urlParams.get('month'));
        if (urlParams.has('year')) updateSalesData('year', urlParams.get('year'));
    });

    function initializeChart() {
        console.log('Initializing daily sales chart');
        
        // Get chart element
        var canvas = document.getElementById('dailySalesChart');
        if (!canvas) {
            console.warn('Sales chart canvas not found');
            return;
        }
        
        // Clear existing chart if it exists
        var existingChart = Chart.getChart(canvas);
        if (existingChart) {
            existingChart.destroy();
        }
        
        var ctx = canvas.getContext('2d');
        var chartData = JSON.parse(canvas.dataset.sales);
        
        // Chart configuration
        const isDark = document.documentElement.classList.contains('dark');
        
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: Object.keys(chartData).map(date => {
                    return new Date(date).toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric'
                    });
                }),
                datasets: [{
                    label: 'Daily Sales',
                    data: Object.values(chartData),
                    borderColor: isDark ? 'rgb(96, 165, 250)' : 'rgb(59, 130, 246)',
                    backgroundColor: isDark ? 'rgba(96, 165, 250, 0.1)' : 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4,
                    fill: true,
                    pointBackgroundColor: isDark ? 'rgb(96, 165, 250)' : 'rgb(59, 130, 246)',
                    pointBorderColor: isDark ? '#1f2937' : '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 4,
                    pointHoverRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: isDark ? 'rgba(0, 0, 0, 0.9)' : 'rgba(0, 0, 0, 0.8)',
                        titleColor: isDark ? '#fff' : '#fff',
                        bodyColor: isDark ? '#fff' : '#fff',
                        padding: 12,
                        titleFont: {
                            size: 14,
                            weight: 'bold'
                        },
                        bodyFont: {
                            size: 13
                        },
                        callbacks: {
                            label: function(context) {
                                return '₱' + context.parsed.y.toLocaleString();
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            callback: value => '₱' + value.toLocaleString(),
                            color: isDark ? '#9ca3af' : '#4b5563',
                            font: {
                                size: 12
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: isDark ? '#9ca3af' : '#4b5563',
                            font: {
                                size: 12
                            }
                        }
                    }
                }
            }
        });
    }

    // Watch for dark mode changes
    if (window.Alpine) {
        Alpine.effect(() => {
            const isDark = document.documentElement.classList.contains('dark');
            initializeChart();
        });
    }
    </script>
    <?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Vsmart System\vsmart sms main\resources\views/dashboard.blade.php ENDPATH**/ ?>